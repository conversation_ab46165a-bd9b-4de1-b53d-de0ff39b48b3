from common.decorators import admin_required
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from wallet.models import Wallet
from wallet.v1.filters import WalletFilter
from wallet.v1.serializers import WalletActivationSerializer, WalletSerializer


class WalletViewSet(viewsets.ModelViewSet):
    queryset = Wallet.objects.all().select_related("business")
    permission_classes = [IsAuthenticated]
    serializer_class = WalletSerializer
    http_method_names = ["get", "post"]
    pagination_class = None
    filter_backends = [
        DjangoFilterBackend,
    ]
    filterset_class = WalletFilter

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(business__owner=self.request.user)
        return queryset

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def retrieve(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @action(
        detail=False,
        methods=["post"],
        serializer_class=WalletActivationSerializer,
        url_path="activate-wallet",
    )
    @admin_required
    def activate_merchant_wallet(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        wallet = serializer.save()
        data = WalletSerializer(wallet).data
        return Response(
            {"message": "Wallet activated successfully", "data": data},
            status=status.HTTP_200_OK,
        )
