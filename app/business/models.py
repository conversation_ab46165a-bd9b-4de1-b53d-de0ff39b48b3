import base64
import secrets

from business.enums import (
    BusinessSection,
    BusinessStatus,
    ChangeRequestStatus,
    DocumentStatus,
    OnboardingStage,
    SocialMediaChannel,
)
from common.models import AuditableModel
from django.db import IntegrityError, models
from user.models import User
from wallet.models import Wallet, WalletEnums


class Business(AuditableModel):
    """Business model for storing business information"""

    name = models.CharField(max_length=255, null=True, blank=True)
    owner = models.OneToOneField(
        "user.User", on_delete=models.PROTECT, related_name="business"
    )
    email = models.EmailField(unique=True, db_index=True, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    phone = models.CharField(max_length=20, unique=True, null=True, blank=True)
    website = models.URLField(unique=True, null=True, blank=True)

    status = models.CharField(
        choices=BusinessStatus.choices(),
        null=True,
        blank=True,
        max_length=20,
        default=BusinessStatus.Inactive.value,
    )

    # Address Info
    office_address = models.CharField(max_length=255, null=True, blank=True)
    street = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=50, null=True, blank=True)
    state = models.CharField(max_length=50, null=True, blank=True)
    postal_code = models.CharField(max_length=20, null=True, blank=True)

    rc_number = models.CharField(max_length=20, unique=True, null=True, blank=True)

    onboarding_stage = models.CharField(
        max_length=40,
        null=True,
        blank=True,
        choices=OnboardingStage.choices,
        default=OnboardingStage.BusinessInformation.value,
    )

    objects = models.Manager()

    def __str__(self):
        if self.name is None:
            return f"UNNAMED BUSINESS -- {self.owner.email}"
        return f"{self.name.upper()} -- {self.owner.email}"

    @property
    def full_address(self):
        return f"{self.office_address}, {self.street}, {self.city}, {self.state}, {self.postal_code}"

    def _create_core_wallets(self):
        for wallet_type in [WalletEnums.GENERAL, WalletEnums.COMMISSION]:
            try:
                Wallet.objects.get_or_create(
                    business=self,
                    type=wallet_type,
                    defaults={"balance": 0},
                )
            except IntegrityError:
                continue

    def _get_wallet(self, wallet_type, for_update=False):
        qs = self.wallets
        if for_update:
            qs = qs.select_for_update()
        return qs.get(type=wallet_type)

    def get_general_wallet(self, for_update=False):
        return self._get_wallet(WalletEnums.GENERAL, for_update=for_update)

    def get_commission_wallet(self, for_update=False):
        return self._get_wallet(WalletEnums.COMMISSION, for_update=for_update)

    def get_kolomoni_va_wallet(self, for_update=False):
        return self._get_wallet(
            WalletEnums.KOLOMONI_VIRTUAL_ACCOUNT, for_update=for_update
        )

    def get_wema_va_wallet(self, for_update=False):
        return self._get_wallet(WalletEnums.WEMA_VIRTUAL_ACCOUNT, for_update=for_update)

    def get_access_va_wallet(self, for_update=False):
        return self._get_wallet(
            WalletEnums.ACCESS_VIRTUAL_ACCOUNT, for_update=for_update
        )

    class Meta:
        verbose_name_plural = "Businesses"
        ordering = ["-created_at"]


class Document(AuditableModel):
    document = models.FileField(
        upload_to="documents",
    )
    document_name = models.CharField(max_length=250, db_index=True)
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="documents",
    )
    status = models.CharField(
        choices=DocumentStatus.choices(),
        max_length=100,
        default=DocumentStatus.Pending.value,
    )
    rejection_message = models.CharField(max_length=500, null=True, blank=True)
    approved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )
    rejected_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )
    approved_at = models.DateTimeField(null=True, blank=True)
    rejected_at = models.DateTimeField(null=True, blank=True)
    resubmitted_at = models.DateTimeField(null=True, blank=True)

    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)

    def __str__(self):
        return self.business.name


class Director(AuditableModel):
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="directors",
    )
    name = models.CharField(max_length=250, db_index=True)
    email = models.EmailField(db_index=True)
    phone = models.CharField(max_length=20)
    bvn = models.CharField(max_length=20)
    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)

    def __str__(self):
        return f"{self.business.name} -- {self.name}"


class SettlementDetail(AuditableModel):
    bank_name = models.CharField(max_length=50)
    bank_code = models.CharField(max_length=10)
    account_number = models.CharField(max_length=10, db_index=True)
    account_name = models.CharField(max_length=100, null=True)
    is_active = models.BooleanField(default=True)
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="settlement_details",
        null=True,
    )

    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)

    def __str__(self):
        return f"{self.business.name} -- {self.account_number} --- {self.account_name} --- {self.bank_name}"


class SocialMedia(AuditableModel):
    """Model for storing business social media channels"""

    business = models.ForeignKey(
        "business.Business",
        on_delete=models.CASCADE,
        related_name="social_media_channels",
    )
    channel = models.CharField(
        max_length=50,
        choices=SocialMediaChannel.choices(),
    )
    url = models.URLField(max_length=255)

    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)
        verbose_name_plural = "Social Media Channels"
        unique_together = ("business", "channel")

    def __str__(self):
        return f"{self.business.name} - {self.channel}"


class APIConfig(AuditableModel):
    business = models.ForeignKey(
        "business.Business", on_delete=models.CASCADE, related_name="business_api_keys"
    )
    private_key = models.CharField(null=True, blank=True)
    public_key = models.CharField(null=True, blank=True, db_index=True)
    webhook_url = models.URLField(null=True, blank=True)
    webhook_signature = models.TextField(null=True, blank=True)
    whitelisted_ips = models.CharField(null=True, blank=True)

    @staticmethod
    def generate_keys():

        public_key = secrets.token_bytes(32)
        private_key = secrets.token_bytes(64)

        return (
            base64.urlsafe_b64encode(public_key).decode("utf-8"),
            base64.urlsafe_b64encode(private_key).decode("utf-8"),
        )

    def __str__(self):
        return self.business.name


class BusinessChangeRequest(AuditableModel):
    SECTION_CHOICES = BusinessSection.choices()

    business = models.ForeignKey(
        "business.Business", on_delete=models.CASCADE, related_name="change_requests"
    )
    section = models.CharField(max_length=100, choices=SECTION_CHOICES)
    old_value = models.JSONField()
    new_value = models.JSONField()
    status = models.CharField(
        max_length=30,
        choices=ChangeRequestStatus.choices(),
        default=ChangeRequestStatus.Pending,
    )
    created_by = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True)
    reviewed_by = models.ForeignKey(
        "user.User", null=True, blank=True, on_delete=models.SET_NULL, related_name="+"
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["business", "section"],
                condition=models.Q(status=ChangeRequestStatus.Pending),
                name="unique_pending_request_per_section",
            )
        ]
