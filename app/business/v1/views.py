from business.handlers.api_config_handler import APIConfigHandler
from business.handlers.onboarding_workflow import OnboardingWorkflowHandler
from business.models import (
    APIConfig,
    Business,
    BusinessChangeRequest,
    Director,
    Document,
    SettlementDetail,
    SocialMedia,
)
from business.v1.serializers import (
    AddDirectorSerializer,
    AddSettlementDetailsSerializer,
    APIConfigSettingsSerializer,
    BusinessChangeRequestMinimalSerializer,
    BusinessInformationChangeRequestSerializer,
    BusinessInformationSerializer,
    DirectorListSerializer,
    DocumentMiniSerializer,
    GeneratePrivateKeySerializer,
    MultipleSocialMediaSerializer,
    SettlementDetailsSerializer,
    SocialMediaSerializer,
    UploadDocumentSerializer,
    VerifyAccountNumberSerializer,
)
from common.decorators import merchant_onboarding_required
from common.serializers import EmptySerializer
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from user.models import User


class OnboardBusinessViewSet(viewsets.GenericViewSet):
    """
    ┌─────────────────────────────┐
    │  Business Onboarding Stages │
    └─────────────────────────────┘

    1️⃣  Business Information
        - Collect basic business details (name, email, phone, etc.)

    2️⃣  Documentation
        - Upload and verify required documents

    3️⃣  Directors Details
        - Capture personal and identification information of key individuals

    4️⃣  Settlement Details
        - Provide bank or payment account information for settlements

    📌 Each stage must be completed in order to successfully onboard a business.
    """

    permission_classes = [IsAuthenticated]
    queryset = User.objects.all()

    @action(
        methods=["POST"],
        detail=False,
        url_path="submit-information",
        serializer_class=BusinessInformationSerializer,
    )
    def submit_business_information(self, request):
        business: Business = request.user.business
        serializer = BusinessInformationSerializer(business, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Business information submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-information",
        serializer_class=BusinessInformationSerializer,
    )
    def business_information(self, request):
        business: Business = request.user.business
        serializer = BusinessInformationSerializer(business)
        return Response(
            {"success": True, "data": serializer.data},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="submit-information-change-request",
        serializer_class=BusinessInformationChangeRequestSerializer,
    )
    @merchant_onboarding_required
    def submit_information_request(self, request):
        business: Business = request.user.business
        serializer = self.get_serializer(instance=business, data=request.data)
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Business information change request submitted.",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        detail=False,
        methods=["GET"],
        url_path="change-requests",
        serializer_class=BusinessChangeRequestMinimalSerializer,
    )
    @merchant_onboarding_required
    def list_change_requests(self, request):
        business = request.user.business
        queryset = BusinessChangeRequest.objects.filter(business=business).order_by(
            "-created_at"
        )
        serializer = self.serializer_class(queryset, many=True)
        return Response(
            {"success": True, "data": serializer.data}, status=status.HTTP_200_OK
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="upload-documents",
        serializer_class=UploadDocumentSerializer,
    )
    def upload_documents(self, request):
        user: User = request.user
        serializer = UploadDocumentSerializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Document uploaded successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-documents",
        serializer_class=DocumentMiniSerializer,
    )
    def fetch_documents(self, request):

        business: Business = request.user.business
        documents = Document.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": DocumentMiniSerializer(documents, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-director",
        serializer_class=AddDirectorSerializer,
    )
    def add_director(self, request):
        user: User = request.user
        serializer = AddDirectorSerializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Director details submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-directors",
        serializer_class=DirectorListSerializer,
    )
    def fetch_directors(self, request):
        """Fetch all directors for the current business"""
        business: Business = request.user.business
        directors = Director.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": DirectorListSerializer(directors, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="delete-director/(?P<director_id>[^/.]+)",
        serializer_class=EmptySerializer,
    )
    def delete_director(self, request, director_id=None):
        business: Business = request.user.business

        try:
            director = Director.objects.get(id=director_id, business=business)
            director.delete()

            return Response(
                {"success": True, "message": "Director deleted successfully"},
                status=status.HTTP_200_OK,
            )
        except Director.DoesNotExist:
            return Response(
                {"success": False, "message": "Director not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-settlement-details",
        serializer_class=AddSettlementDetailsSerializer,
    )
    def add_settlement_details(self, request):
        user: User = request.user
        serializer = AddSettlementDetailsSerializer(
            data=request.data, context={"user": user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Settlement details submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-settlement-details",
        serializer_class=SettlementDetailsSerializer,
    )
    def fetch_settlement_details(self, request):
        business: User = request.user.business

        settlement_details = SettlementDetail.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": SettlementDetailsSerializer(settlement_details, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="verify-account-number",
        serializer_class=VerifyAccountNumberSerializer,
    )
    def verify_account_number(self, request):
        serializer = VerifyAccountNumberSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        result = serializer.save()

        return Response(
            {"success": True, "data": result},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="bank-list",
        serializer_class=EmptySerializer,
    )
    def get_bank_list(self, request):
        # TODO fetch from the bank model
        return Response(
            {
                "success": True,
                "data": [{"bank_name": "Sagecloud Test Bank", "bank_code": "012345"}],
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="onboarding-stages",
        serializer_class=EmptySerializer,
    )
    def onboarding_stages(self, request):
        business: Business = request.user.business
        return Response(
            OnboardingWorkflowHandler(business).onboarding_stages(),
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-social-media",
        serializer_class=MultipleSocialMediaSerializer,
    )
    def add_social_media(self, request):
        """Add or update a social media channel for the business"""
        user: User = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Social media channel added successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="social-media",
        serializer_class=SocialMediaSerializer,
    )
    def list_social_media(self, request):
        """List all social media channels for the business"""
        business: Business = request.user.business
        social_media = SocialMedia.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": SocialMediaSerializer(social_media, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="social-media/(?P<social_media_id>[^/.]+)",
        serializer_class=EmptySerializer,
    )
    # TODO: Maybe we take this out??
    def delete_social_media(self, request, social_media_id=None):
        """Delete a social media channel"""
        business: Business = request.user.business

        try:
            social_media = SocialMedia.objects.get(
                id=social_media_id, business=business
            )
            social_media.delete()

            return Response(
                {
                    "success": True,
                    "message": "Social media channel deleted successfully",
                },
                status=status.HTTP_200_OK,
            )
        except SocialMedia.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "message": "Social media channel not found",
                },
                status=status.HTTP_404_NOT_FOUND,
            )


class APIConfigViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    queryset = APIConfig.objects.all()

    @action(
        methods=["GET"],
        detail=False,
        url_path="public-key",
        serializer_class=EmptySerializer,
    )
    def get_public_key(self, request):
        business: Business = request.user.business
        public_key = APIConfigHandler().get_public_key(business)

        return Response(
            {
                "success": True,
                "data": {"public_key": public_key},
            }
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="generate-private-key",
        serializer_class=GeneratePrivateKeySerializer,
    )
    @merchant_onboarding_required
    def generate_private_key(self, request):
        user: User = request.user
        serializer = GeneratePrivateKeySerializer(
            data=request.data, context={"user": user}
        )
        serializer.is_valid(raise_exception=True)
        private_key = serializer.save()

        return Response(
            {
                "success": True,
                "data": {"private_key": private_key},
            }
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="settings",
        serializer_class=APIConfigSettingsSerializer,
    )
    @merchant_onboarding_required
    def config_settings(self, request):
        business: Business = request.user.business
        serializer = APIConfigSettingsSerializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {
                "success": True,
                "message": "Settings updated successfully.",
            }
        )
