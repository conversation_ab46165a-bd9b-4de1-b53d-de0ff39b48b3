import logging

from rest_framework import serializers
from transaction.enums import TransactionClassEnum
from transaction.models import (
    AirtimeVASTransaction,
    BettingVASTransaction,
    CableTVVASTransaction,
    DataVASTransaction,
    EducationVASTransaction,
    ElectricityVASTransaction,
    EpinVASTransaction,
    KYCVASTransaction,
    Transaction,
    VirtualAccountVasTransaction,
)

logger = logging.getLogger(__name__)


class TransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = "__all__"


class TransactionOverviewSerializer(serializers.Serializer):
    total_value = serializers.FloatField()
    total_count = serializers.IntegerField()
    total_commission = serializers.IntegerField()
    pending_count = serializers.IntegerField()
    failed_count = serializers.IntegerField()
    successful_count = serializers.IntegerField()


class BaseVASTransactionSerializer(serializers.ModelSerializer):
    def to_representation(self, instance):
        data = super().to_representation(instance)
        business = instance.business
        data["merchant_name"] = business.name
        return data


class AirtimeVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = AirtimeVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.AIRTIME.value
        return data


class BettingVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = BettingVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.BETTING.value
        return data


class CableTVVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = CableTVVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.CABLE_TV.value
        return data


class DataVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = DataVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.DATA.value
        return data


class EpinVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = EpinVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.EPIN.value
        return data


class ElectricityVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = ElectricityVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.ELECTRICITY.value
        return data


class EducationVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = EducationVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.EDUCATION.value
        return data


class KYCVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = KYCVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.KYC.value
        return data


class VirtualAccountVasTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = VirtualAccountVasTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.VIRTUAL_ACCOUNT.value
        return data


TXN_CLASS_SERIALIZER_MAP = {
    TransactionClassEnum.AIRTIME.value: {
        "model": AirtimeVASTransaction,
        "serializer": AirtimeVASTransactionSerializer,
    },
    TransactionClassEnum.BETTING.value: {
        "model": BettingVASTransaction,
        "serializer": BettingVASTransactionSerializer,
    },
    TransactionClassEnum.CABLE_TV.value: {
        "model": CableTVVASTransaction,
        "serializer": CableTVVASTransactionSerializer,
    },
    TransactionClassEnum.DATA.value: {
        "model": DataVASTransaction,
        "serializer": DataVASTransactionSerializer,
    },
    TransactionClassEnum.ELECTRICITY.value: {
        "model": ElectricityVASTransaction,
        "serializer": ElectricityVASTransactionSerializer,
    },
    TransactionClassEnum.EPIN.value: {
        "model": EpinVASTransaction,
        "serializer": EpinVASTransactionSerializer,
    },
    TransactionClassEnum.EDUCATION.value: {
        "model": EducationVASTransaction,
        "serializer": EducationVASTransactionSerializer,
    },
    TransactionClassEnum.KYC.value: {
        "model": KYCVASTransaction,
        "serializer": KYCVASTransactionSerializer,
    },
    TransactionClassEnum.VIRTUAL_ACCOUNT.value: {
        "model": VirtualAccountVasTransaction,
        "serializer": VirtualAccountVasTransactionSerializer,
    },
}


class TransactionPolymorphicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = "__all__"

    def to_representation(self, instance):
        txn_class = getattr(instance, "txn_class", None)
        mapping = TXN_CLASS_SERIALIZER_MAP.get(txn_class)

        if mapping:
            model_cls = mapping["model"]
            serializer_cls = mapping["serializer"]

            detailed_instance = self._get_detailed_instance(model_cls, instance)

            if detailed_instance:
                data = serializer_cls(detailed_instance, context=self.context).data
                data["base_txn_id"] = str(instance.id)
                return data

        data = TransactionSerializer(instance, context=self.context).data
        data["base_txn_id"] = str(instance.id)
        return data

    def _get_detailed_instance(self, model_cls, instance):
        lookup_fields = ("id", "reference", "merchant_reference")
        for field in lookup_fields:
            value = getattr(instance, field, None)
            if value:
                try:
                    return model_cls.objects.get(**{field: value})
                except model_cls.DoesNotExist:
                    continue
        return None
