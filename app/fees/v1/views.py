from fees.models import <PERSON><PERSON><PERSON>, VenderFee
from fees.v1.serializers import (
    BusinessFeeSerializer,
    CreateBusinessFeeSerializer,
    CreateVenderFeeSerializer,
    VenderFeeSerializer,
)
from rest_framework import status, viewsets
from rest_framework.response import Response


class BusinessFeeViewSet(viewsets.ModelViewSet):
    queryset = BusinessFee.objects.prefetch_related("business_fee_bands").all()
    serializer_class = BusinessFeeSerializer

    def get_serializer_class(self):
        if self.action == "create":
            return CreateBusinessFeeSerializer
        return BusinessFeeSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)


class VenderFeeViewSet(viewsets.ModelViewSet):
    queryset = VenderFee.objects.prefetch_related("vender_fee_bands").all()
    serializer_class = VenderFeeSerializer

    def get_serializer_class(self):
        if self.action == "create":
            return CreateVenderFeeSerializer
        return VenderFeeSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)
