#!/usr/bin/env python
"""
Test script to verify the export system works correctly.
Run this after setting up the export fields.

Usage:
    python manage.py shell < exports/test_export_system.py
"""

import os
import django
from django.conf import settings

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.base')
django.setup()

from django.contrib.auth import get_user_model
from business.models import Business
from exports.models import ExportRequest, ExportField
from exports.services import ExportService
from exports.serializers import CreateExportRequestSerializer

User = get_user_model()

def test_export_system():
    """Test the export system functionality"""
    print("🧪 Testing Export System...")
    
    # Test 1: Check if export fields are configured
    print("\n1. Checking export field configuration...")
    
    models_to_check = ['transaction.Transaction', 'audit.AuditLog', 'wallet.Wallet']
    for model_name in models_to_check:
        field_count = ExportField.objects.filter(model_name=model_name).count()
        default_count = ExportField.objects.filter(model_name=model_name, is_default=True).count()
        print(f"   ✓ {model_name}: {field_count} fields ({default_count} default)")
        
        if field_count == 0:
            print(f"   ❌ No fields configured for {model_name}. Run: python manage.py setup_export_fields")
            return False
    
    # Test 2: Test ExportService
    print("\n2. Testing ExportService...")
    
    try:
        service = ExportService()
        
        # Test getting available fields
        available_fields = service.get_available_fields('transaction.Transaction')
        print(f"   ✓ Available fields for transactions: {len(available_fields)}")
        
        # Test getting default fields
        default_fields = service.get_default_fields('transaction.Transaction')
        print(f"   ✓ Default fields for transactions: {len(default_fields)}")
        
        if not default_fields:
            print("   ❌ No default fields configured")
            return False
            
    except Exception as e:
        print(f"   ❌ ExportService error: {str(e)}")
        return False
    
    # Test 3: Test serializer validation
    print("\n3. Testing serializer validation...")
    
    try:
        # Test with valid data
        valid_data = {
            'export_type': 'transactions',
            'model_name': 'transaction.Transaction',
            'fields_to_export': default_fields[:3],  # Use first 3 default fields
            'filters': {
                'date_from': '2023-01-01T00:00:00',
                'date_to': '2023-12-31T23:59:59'
            }
        }
        
        serializer = CreateExportRequestSerializer(data=valid_data)
        if serializer.is_valid():
            print("   ✓ Valid data passes validation")
        else:
            print(f"   ❌ Valid data failed validation: {serializer.errors}")
            return False
        
        # Test with empty fields (should auto-select defaults)
        empty_fields_data = {
            'export_type': 'transactions',
            'model_name': 'transaction.Transaction',
            'fields_to_export': [],
            'filters': {}
        }
        
        serializer = CreateExportRequestSerializer(data=empty_fields_data)
        if serializer.is_valid():
            validated_data = serializer.validated_data
            if validated_data['fields_to_export']:
                print("   ✓ Empty fields auto-populated with defaults")
            else:
                print("   ❌ Empty fields not auto-populated")
                return False
        else:
            print(f"   ❌ Empty fields validation failed: {serializer.errors}")
            return False
            
    except Exception as e:
        print(f"   ❌ Serializer test error: {str(e)}")
        return False
    
    # Test 4: Test model creation
    print("\n4. Testing model creation...")
    
    try:
        # Create test user and business if they don't exist
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'firstname': 'Test',
                'lastname': 'User',
                'role': 'business_owner'
            }
        )
        
        business, created = Business.objects.get_or_create(
            owner=user,
            defaults={'name': 'Test Business'}
        )
        
        # Test creating export request
        export_request = ExportRequest.objects.create(
            user=user,
            business=business,
            export_type=ExportRequest.ExportType.TRANSACTIONS,
            model_name='transaction.Transaction',
            fields_to_export=['id', 'reference', 'amount'],
            filename='test_export.csv'
        )
        
        print(f"   ✓ Export request created: {export_request.id}")
        print(f"   ✓ Status: {export_request.status}")
        print(f"   ✓ Progress: {export_request.progress_percentage}%")
        
        # Clean up
        export_request.delete()
        
    except Exception as e:
        print(f"   ❌ Model creation error: {str(e)}")
        return False
    
    # Test 5: Test field value extraction
    print("\n5. Testing field value extraction...")
    
    try:
        service = ExportService()
        
        # Test with the business object
        test_fields = ['id', 'name', 'owner.email']
        for field_name in test_fields:
            try:
                value = service._get_field_value(business, field_name)
                print(f"   ✓ {field_name}: {value}")
            except Exception as e:
                print(f"   ❌ Error extracting {field_name}: {str(e)}")
                return False
                
    except Exception as e:
        print(f"   ❌ Field extraction test error: {str(e)}")
        return False
    
    print("\n🎉 All tests passed! Export system is working correctly.")
    print("\nNext steps:")
    print("1. Run migrations: docker-compose exec api python manage.py migrate")
    print("2. Setup fields: docker-compose exec api python manage.py setup_export_fields")
    print("3. Test API endpoints with your frontend or API client")
    
    return True

if __name__ == '__main__':
    test_export_system()
