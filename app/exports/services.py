import csv
import os
import tempfile
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.files.storage import default_storage
from django.db import models
from django.utils import timezone
from django.utils.text import slugify
import boto3
from botocore.exceptions import ClientError
import logging

from .models import ExportRequest, ExportField

logger = logging.getLogger(__name__)


class ExportService:
    """
    Generic service for exporting data from any Django model to CSV
    """
    
    def __init__(self):
        self.s3_client = self._get_s3_client()
        self.bucket_name = getattr(settings, 'AWS_STORAGE_BUCKET_NAME')
        self.export_folder = 'exports'
    
    def _get_s3_client(self):
        """Initialize S3 client"""
        return boto3.client(
            's3',
            aws_access_key_id=getattr(settings, 'AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=getattr(settings, 'AWS_SECRET_ACCESS_KEY'),
            region_name=getattr(settings, 'AWS_S3_REGION_NAME'),
            endpoint_url=getattr(settings, 'AWS_S3_ENDPOINT_URL', None)
        )
    
    def create_export_request(
        self,
        user,
        business,
        model_name: str,
        export_type: str,
        fields_to_export: List[str],
        filters: Dict[str, Any] = None
    ) -> ExportRequest:
        """
        Create a new export request
        """
        try:
            # Validate business ownership
            if not business or business.owner != user:
                raise ValueError("User can only create exports for their own business")

            # Validate fields are not empty
            if not fields_to_export:
                raise ValueError("fields_to_export cannot be empty")

            # Get the model class
            model_class = self._get_model_class(model_name)
            content_type = ContentType.objects.get_for_model(model_class)

            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_business_name = slugify(business.name or 'business') or 'business'
            filename = f"{slugify(export_type)}_{safe_business_name}_{timestamp}.csv"

            # Create export request
            export_request = ExportRequest.objects.create(
                user=user,
                business=business,
                export_type=export_type,
                content_type=content_type,
                model_name=model_name,
                fields_to_export=fields_to_export,
                filters=filters or {},
                filename=filename,
                expires_at=timezone.now() + timedelta(days=7)  # 7 days expiration
            )

            logger.info(f"Created export request {export_request.id} for user {user.email}")
            return export_request

        except Exception as e:
            logger.error(f"Error creating export request: {str(e)}")
            raise
    
    def process_export(self, export_request: ExportRequest) -> bool:
        """
        Process the export request and generate CSV file
        """
        try:
            export_request.status = ExportRequest.Status.PROCESSING
            export_request.save(update_fields=['status'])
            
            # Get model and queryset
            model_class = self._get_model_class(export_request.model_name)
            queryset = self._build_queryset(model_class, export_request)
            
            # Count total records
            total_records = queryset.count()
            export_request.total_records = total_records
            export_request.save(update_fields=['total_records'])
            
            if total_records == 0:
                export_request.status = ExportRequest.Status.COMPLETED
                export_request.error_message = "No data found matching the criteria"
                export_request.save(update_fields=['status', 'error_message'])
                return True
            
            # Generate CSV file
            file_path = self._generate_csv_file(export_request, queryset)
            
            # Upload to S3
            s3_key = f"{self.export_folder}/{export_request.filename}"
            self._upload_to_s3(file_path, s3_key)
            
            # Generate presigned URL
            download_url = self._generate_presigned_url(s3_key)
            
            # Update export request
            export_request.file_path = s3_key
            export_request.download_url = download_url
            export_request.status = ExportRequest.Status.COMPLETED
            export_request.processed_records = total_records
            
            # Get file size
            if os.path.exists(file_path):
                export_request.file_size = os.path.getsize(file_path)
                os.remove(file_path)  # Clean up local file
            
            export_request.save()
            
            logger.info(f"Successfully processed export request {export_request.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing export request {export_request.id}: {str(e)}")
            export_request.status = ExportRequest.Status.FAILED
            export_request.error_message = str(e)
            export_request.save(update_fields=['status', 'error_message'])
            return False
    
    def _get_model_class(self, model_name: str):
        """Get Django model class from string"""
        try:
            app_label, model_name_only = model_name.split('.')
            return apps.get_model(app_label, model_name_only)
        except (ValueError, LookupError) as e:
            raise ValueError(f"Invalid model name: {model_name}") from e
    
    def _build_queryset(self, model_class, export_request: ExportRequest):
        """Build filtered queryset based on export request"""
        queryset = model_class.objects.all()
        
        # Apply business filtering if the model has a business field
        if hasattr(model_class, 'business'):
            queryset = queryset.filter(business=export_request.business)
        elif hasattr(model_class, 'user') and hasattr(export_request.business, 'owner'):
            # For models that don't have business but have user (like audit logs)
            queryset = queryset.filter(user=export_request.business.owner)
        
        # Apply additional filters
        filters = export_request.filters
        if filters:
            # Handle date range filters
            if 'date_from' in filters and filters['date_from']:
                queryset = queryset.filter(created_at__gte=filters['date_from'])
            if 'date_to' in filters and filters['date_to']:
                queryset = queryset.filter(created_at__lte=filters['date_to'])
            
            # Handle other filters
            for key, value in filters.items():
                if key not in ['date_from', 'date_to'] and value:
                    try:
                        queryset = queryset.filter(**{key: value})
                    except Exception as e:
                        logger.warning(f"Invalid filter {key}={value}: {str(e)}")
        
        return queryset.order_by('-created_at')
    
    def _generate_csv_file(self, export_request: ExportRequest, queryset) -> str:
        """Generate CSV file from queryset"""
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
        
        try:
            writer = csv.writer(temp_file)
            
            # Write headers
            headers = self._get_field_headers(export_request.fields_to_export, export_request.model_name)
            writer.writerow(headers)
            
            # Write data in chunks to handle large datasets
            chunk_size = 1000
            processed = 0
            
            for chunk in self._chunked_queryset(queryset, chunk_size):
                rows = []
                for obj in chunk:
                    row = self._extract_row_data(obj, export_request.fields_to_export)
                    rows.append(row)
                
                writer.writerows(rows)
                processed += len(chunk)
                
                # Update progress
                export_request.processed_records = processed
                export_request.save(update_fields=['processed_records'])
            
            temp_file.close()
            return temp_file.name
            
        except Exception as e:
            temp_file.close()
            if os.path.exists(temp_file.name):
                os.remove(temp_file.name)
            raise e

    def _get_field_headers(self, fields_to_export: List[str], model_name: str) -> List[str]:
        """Get human-readable headers for CSV columns"""
        headers = []
        for field_name in fields_to_export:
            try:
                export_field = ExportField.objects.get(
                    model_name=model_name,
                    field_name=field_name
                )
                headers.append(export_field.field_label)
            except ExportField.DoesNotExist:
                # Fallback to field name if not configured
                headers.append(field_name.replace('_', ' ').title())
        return headers

    def _extract_row_data(self, obj, fields_to_export: List[str]) -> List[str]:
        """Extract data from model instance for CSV row"""
        row = []
        for field_name in fields_to_export:
            try:
                value = self._get_field_value(obj, field_name)
                row.append(str(value) if value is not None else '')
            except Exception as e:
                logger.warning(f"Error extracting field {field_name}: {str(e)}")
                row.append('')
        return row

    def _get_field_value(self, obj, field_name: str):
        """Get field value from model instance, handling nested fields"""
        if '.' in field_name:
            # Handle nested fields like 'business.name'
            parts = field_name.split('.')
            value = obj
            for part in parts:
                if value is None:
                    return None
                value = getattr(value, part, None)
            return value
        else:
            return getattr(obj, field_name, None)

    def _chunked_queryset(self, queryset, chunk_size: int):
        """Yield chunks of queryset to handle large datasets efficiently"""
        # Use iterator() to avoid loading all objects into memory
        # and process in chunks using primary key ranges
        last_pk = None
        while True:
            if last_pk:
                chunk = queryset.filter(pk__gt=last_pk)[:chunk_size]
            else:
                chunk = queryset[:chunk_size]

            chunk_list = list(chunk)
            if not chunk_list:
                break

            yield chunk_list
            last_pk = chunk_list[-1].pk

    def _upload_to_s3(self, file_path: str, s3_key: str):
        """Upload file to S3"""
        try:
            self.s3_client.upload_file(file_path, self.bucket_name, s3_key)
            logger.info(f"Successfully uploaded {s3_key} to S3")
        except ClientError as e:
            logger.error(f"Error uploading to S3: {str(e)}")
            raise

    def _generate_presigned_url(self, s3_key: str, expiration: int = 604800) -> str:
        """Generate presigned URL for S3 object (default 7 days)"""
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            return url
        except ClientError as e:
            logger.error(f"Error generating presigned URL: {str(e)}")
            raise

    def get_available_fields(self, model_name: str) -> List[Dict[str, Any]]:
        """Get available fields for export for a given model"""
        fields = ExportField.objects.filter(model_name=model_name).order_by('order', 'field_name')
        return [
            {
                'field_name': field.field_name,
                'field_label': field.field_label,
                'field_type': field.field_type,
                'is_default': field.is_default,
                'is_sensitive': field.is_sensitive
            }
            for field in fields
        ]

    def get_default_fields(self, model_name: str) -> List[str]:
        """Get default fields for export for a given model"""
        fields = ExportField.objects.filter(
            model_name=model_name,
            is_default=True
        ).order_by('order', 'field_name')
        return [field.field_name for field in fields]

    def cleanup_expired_exports(self):
        """Clean up expired export files from S3"""
        expired_exports = ExportRequest.objects.filter(
            expires_at__lt=timezone.now(),
            status=ExportRequest.Status.COMPLETED
        )

        for export_request in expired_exports:
            try:
                if export_request.file_path:
                    self.s3_client.delete_object(
                        Bucket=self.bucket_name,
                        Key=export_request.file_path
                    )
                export_request.mark_as_expired()
                logger.info(f"Cleaned up expired export {export_request.id}")
            except Exception as e:
                logger.error(f"Error cleaning up export {export_request.id}: {str(e)}")


class ExportFieldManager:
    """
    Manager for setting up exportable fields for models
    """

    @staticmethod
    def setup_model_fields(model_name: str, field_configs: List[Dict[str, Any]]):
        """
        Setup exportable fields for a model

        field_configs format:
        [
            {
                'field_name': 'id',
                'field_label': 'ID',
                'field_type': 'CharField',
                'is_default': True,
                'is_sensitive': False,
                'order': 1
            },
            ...
        ]
        """
        # Clear existing fields for this model
        ExportField.objects.filter(model_name=model_name).delete()

        # Create new fields
        fields_to_create = []
        for config in field_configs:
            fields_to_create.append(
                ExportField(
                    model_name=model_name,
                    field_name=config['field_name'],
                    field_label=config['field_label'],
                    field_type=config.get('field_type', 'CharField'),
                    is_default=config.get('is_default', False),
                    is_sensitive=config.get('is_sensitive', False),
                    order=config.get('order', 0)
                )
            )

        ExportField.objects.bulk_create(fields_to_create)
        logger.info(f"Setup {len(fields_to_create)} fields for model {model_name}")
