import logging
from celery import shared_task
from django.template.loader import get_template
from django.conf import settings
from django.utils import timezone

from .models import ExportRequest
from .services import ExportService
from user.utils import send_email

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_export_task(self, export_request_id: int):
    """
    Celery task to process export request in background
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)
        logger.info(f"Processing export request {export_request_id}")
        
        # Process the export
        export_service = ExportService()
        success = export_service.process_export(export_request)
        
        if success:
            # Send email notification
            send_export_ready_email.delay(export_request_id)
            logger.info(f"Successfully processed export request {export_request_id}")
        else:
            logger.error(f"Failed to process export request {export_request_id}")
            
    except ExportRequest.DoesNotExist:
        logger.error(f"Export request {export_request_id} not found")
    except Exception as exc:
        logger.error(f"Error processing export request {export_request_id}: {str(exc)}")
        
        # Retry the task
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying export request {export_request_id} (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        else:
            # Mark as failed after max retries
            try:
                export_request = ExportRequest.objects.get(id=export_request_id)
                export_request.status = ExportRequest.Status.FAILED
                export_request.error_message = f"Failed after {self.max_retries} retries: {str(exc)}"
                export_request.save()
            except ExportRequest.DoesNotExist:
                pass


@shared_task
def send_export_ready_email(export_request_id: int):
    """
    Send email notification when export is ready for download
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)
        
        if export_request.email_sent:
            logger.info(f"Email already sent for export request {export_request_id}")
            return
        
        # Prepare email data
        email_data = {
            'user_name': export_request.user.fullname,
            'export_type': export_request.get_export_type_display(),
            'filename': export_request.filename,
            'download_url': export_request.download_url,
            'total_records': export_request.total_records,
            'file_size_mb': round(export_request.file_size / (1024 * 1024), 2) if export_request.file_size else 0,
            'expires_at': export_request.expires_at,
            'business_name': export_request.business.name or 'Your Business'
        }
        
        # Load email templates
        html_template = get_template('emails/export_ready_template.html')
        text_template = get_template('emails/export_ready_template.txt')
        
        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)
        
        # Send email
        subject = f"Your {export_request.get_export_type_display()} Export is Ready"
        send_email(subject, export_request.user.email, html_content, text_content)
        
        # Mark email as sent
        export_request.email_sent = True
        export_request.email_sent_at = timezone.now()
        export_request.save(update_fields=['email_sent', 'email_sent_at'])
        
        logger.info(f"Export ready email sent for request {export_request_id}")
        
    except ExportRequest.DoesNotExist:
        logger.error(f"Export request {export_request_id} not found")
    except Exception as exc:
        logger.error(f"Error sending export ready email for request {export_request_id}: {str(exc)}")


@shared_task
def cleanup_expired_exports():
    """
    Periodic task to clean up expired export files
    """
    try:
        export_service = ExportService()
        export_service.cleanup_expired_exports()
        logger.info("Completed cleanup of expired exports")
    except Exception as exc:
        logger.error(f"Error during export cleanup: {str(exc)}")


@shared_task
def send_export_failed_email(export_request_id: int):
    """
    Send email notification when export fails
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)
        
        # Prepare email data
        email_data = {
            'user_name': export_request.user.fullname,
            'export_type': export_request.get_export_type_display(),
            'error_message': export_request.error_message,
            'business_name': export_request.business.name or 'Your Business'
        }
        
        # Load email templates
        html_template = get_template('emails/export_failed_template.html')
        text_template = get_template('emails/export_failed_template.txt')
        
        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)
        
        # Send email
        subject = f"Export Failed - {export_request.get_export_type_display()}"
        send_email(subject, export_request.user.email, html_content, text_content)
        
        logger.info(f"Export failed email sent for request {export_request_id}")
        
    except ExportRequest.DoesNotExist:
        logger.error(f"Export request {export_request_id} not found")
    except Exception as exc:
        logger.error(f"Error sending export failed email for request {export_request_id}: {str(exc)}")


@shared_task
def bulk_export_task(export_request_ids: list):
    """
    Process multiple export requests in bulk
    """
    for export_request_id in export_request_ids:
        process_export_task.delay(export_request_id)
