from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.utils import timezone
import logging

from .models import ExportRequest, ExportField
from .serializers import (
    ExportRequestSerializer,
    CreateExportRequestSerializer,
   # AvailableFieldsSerializer,
    ExportStatusSerializer,
    #BulkExportRequestSerializer
)
from .services import ExportService
from .tasks import process_export_task, bulk_export_task

logger = logging.getLogger(__name__)


class ExportViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing data exports
    """
    
    serializer_class = ExportRequestSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status', 'export_type', 'created_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter exports by user's business"""
        user = self.request.user
        if hasattr(user, 'business'):
            return ExportRequest.objects.filter(business=user.business)
        return ExportRequest.objects.none()
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return CreateExportRequestSerializer
        # elif self.action == 'available_fields':
        #     return AvailableFieldsSerializer
        # elif self.action == 'bulk_create':
        #     return BulkExportRequestSerializer
        return ExportRequestSerializer
    
    def create(self, request, *args, **kwargs):
        """Create a new export request"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        if not hasattr(user, 'business') or not user.business:
            return Response(
                {'error': 'User must be associated with a business'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Additional permission check for audit logs
        model_name = serializer.validated_data['model_name']
        if model_name == 'audit.AuditLog' and user != user.business.owner:
            return Response(
                {'error': 'Only business owners can export audit logs'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            # Create export request
            export_service = ExportService()
            export_request = export_service.create_export_request(
                user=user,
                business=user.business,
                model_name=model_name,
                export_type=serializer.validated_data['export_type'],
                fields_to_export=serializer.validated_data['fields_to_export'],
                filters=serializer.validated_data.get('filters', {})
            )

            # Start background processing
            process_export_task.delay(export_request.id)

            # Return created export request
            response_serializer = ExportRequestSerializer(export_request)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except ValueError as e:
            logger.warning(f"Validation error creating export request: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error creating export request: {str(e)}")
            return Response(
                {'error': 'Failed to create export request'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    # @action(detail=False, methods=['post'])
    # def bulk_create(self, request):
    #     """Create multiple export requests"""
    #     serializer = self.get_serializer(data=request.data)
    #     serializer.is_valid(raise_exception=True)
        
    #     user = request.user
    #     if not hasattr(user, 'business'):
    #         return Response(
    #             {'error': 'User must be associated with a business'},
    #             status=status.HTTP_400_BAD_REQUEST
    #         )
        
    #     try:
    #         export_service = ExportService()
    #         export_requests = []
    #         export_request_ids = []
            
    #         for export_data in serializer.validated_data['export_requests']:
    #             export_request = export_service.create_export_request(
    #                 user=user,
    #                 business=user.business,
    #                 model_name=export_data['model_name'],
    #                 export_type=export_data['export_type'],
    #                 fields_to_export=export_data['fields_to_export'],
    #                 filters=export_data.get('filters', {})
    #             )
    #             export_requests.append(export_request)
    #             export_request_ids.append(export_request.id)
            
    #         # Start bulk processing
    #         bulk_export_task.delay(export_request_ids)
            
    #         # Return created export requests
    #         response_serializer = ExportRequestSerializer(export_requests, many=True)
    #         return Response(response_serializer.data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error creating bulk export requests: {str(e)}")
            return Response(
                {'error': 'Failed to create bulk export requests'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """Get export status"""
        export_request = self.get_object()
        serializer = ExportStatusSerializer({
            'id': export_request.id,
            'status': export_request.status,
            'progress_percentage': export_request.progress_percentage,
            'total_records': export_request.total_records,
            'processed_records': export_request.processed_records,
            'error_message': export_request.error_message,
            'download_url': export_request.download_url,
            'expires_at': export_request.expires_at
        })
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel an export request"""
        export_request = self.get_object()
        
        if export_request.status in [ExportRequest.Status.COMPLETED, ExportRequest.Status.FAILED]:
            return Response(
                {'error': 'Cannot cancel completed or failed export'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        export_request.status = ExportRequest.Status.FAILED
        export_request.error_message = 'Cancelled by user'
        export_request.save()
        
        return Response({'message': 'Export cancelled successfully'})
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Get download URL for completed export"""
        export_request = self.get_object()
        
        if export_request.status != ExportRequest.Status.COMPLETED:
            return Response(
                {'error': 'Export is not completed yet'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if export_request.is_expired:
            return Response(
                {'error': 'Export has expired'},
                status=status.HTTP_410_GONE
            )
        
        return Response({
            'download_url': export_request.download_url,
            'filename': export_request.filename,
            'expires_at': export_request.expires_at
        })
    
    # @action(detail=False, methods=['post'])
    # def available_fields(self, request):
    #     """Get available fields for a model"""
    #     serializer = self.get_serializer(data=request.data)
    #     serializer.is_valid(raise_exception=True)
        
    #     model_name = serializer.validated_data['model_name']
    #     export_service = ExportService()
        
    #     try:
    #         available_fields = export_service.get_available_fields(model_name)
    #         default_fields = export_service.get_default_fields(model_name)
            
    #         return Response({
    #             'model_name': model_name,
    #             'available_fields': available_fields,
    #             'default_fields': default_fields
    #         })
            
    #     except Exception as e:
    #         logger.error(f"Error getting available fields for {model_name}: {str(e)}")
    #         return Response(
    #             {'error': 'Failed to get available fields'},
    #             status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #         )
    
    @action(detail=False, methods=['get'])
    def supported_models(self, request):
        """Get list of supported models for export"""
        supported_models = CreateExportRequestSerializer.SUPPORTED_MODELS
        return Response({
            'supported_models': [
                {
                    'key': key,
                    'model_name': model_name,
                    'display_name': key.replace('_', ' ').title()
                }
                for key, model_name in supported_models.items()
            ]
        })
    
    @action(detail=False, methods=['delete'])
    def cleanup_expired(self, request):
        """Manually trigger cleanup of expired exports (admin only)"""
        user = request.user
        if not (user.is_staff or getattr(user, 'role', '') in ['admin', 'super_admin']):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        try:
            from .tasks import cleanup_expired_exports
            cleanup_expired_exports.delay()
            return Response({'message': 'Cleanup task started'})
        except Exception as e:
            logger.error(f"Error starting cleanup task: {str(e)}")
            return Response(
                {'error': 'Failed to start cleanup task'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
