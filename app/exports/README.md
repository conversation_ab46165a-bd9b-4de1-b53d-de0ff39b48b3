# Data Export System

A flexible, scalable data export system for SageCloud that allows users to export data from any supported model to CSV format, with files stored in S3 and download links sent via email.

## Features

- **Generic Export System**: Export data from any Django model
- **Field Selection**: Users can choose which fields to export
- **Filtering Support**: Date ranges, status filters, and custom filters
- **Background Processing**: Uses Celery for asynchronous processing
- **S3 Storage**: Files are stored in S3 with presigned URLs
- **Email Notifications**: Users receive download links via email
- **Security**: Business owners can only export their own data
- **File Expiration**: Download links expire after 7 days
- **Export History**: Track all export requests and their status

## Supported Models

- `transaction.Transaction` - Transaction data
- `audit.AuditLog` - Audit log data (business owners only)
- `wallet.Wallet` - Wallet/commission data

## API Endpoints

### Get Supported Models
```
GET /api/v1/exports/supported_models/
```

### Get Available Fields
```
POST /api/v1/exports/available_fields/
{
    "model_name": "transaction.Transaction"
}
```

### Create Export Request
```
POST /api/v1/exports/
{
    "export_type": "transactions",
    "model_name": "transaction.Transaction",
    "fields_to_export": ["id", "reference", "amount", "status", "created_at"],
    "filters": {
        "date_from": "2023-01-01T00:00:00",
        "date_to": "2023-12-31T23:59:59",
        "status": "completed"
    }
}
```

### List Export Requests
```
GET /api/v1/exports/
```

### Get Export Status
```
GET /api/v1/exports/{id}/status/
```

### Get Download URL
```
GET /api/v1/exports/{id}/download/
```

### Cancel Export
```
POST /api/v1/exports/{id}/cancel/
```

### Bulk Export
```
POST /api/v1/exports/bulk_create/
{
    "export_requests": [
        {
            "export_type": "transactions",
            "model_name": "transaction.Transaction",
            "fields_to_export": ["id", "reference", "amount"]
        },
        {
            "export_type": "audit_logs",
            "model_name": "audit.AuditLog",
            "fields_to_export": ["id", "action", "created_at"]
        }
    ]
}
```

## Setup Instructions

### 1. Run Migrations
```bash
docker-compose exec api python manage.py makemigrations exports
docker-compose exec api python manage.py migrate
```

### 2. Setup Export Fields
```bash
docker-compose exec api python manage.py setup_export_fields
```

### 3. Update Celery Worker
Make sure your Celery worker is configured to handle the export queue:
```bash
celery -A core worker --loglevel=info --queues=export_queue
```

### 4. Environment Variables
Ensure these environment variables are set:
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AWS_STORAGE_BUCKET_NAME`
- `AWS_S3_REGION_NAME`
- `AWS_S3_ENDPOINT_URL` (for DigitalOcean Spaces)

## Usage Examples

### Export Transactions for Last Month
```python
import requests

data = {
    "export_type": "transactions",
    "model_name": "transaction.Transaction",
    "fields_to_export": [
        "id", "reference", "merchant_reference", "status", 
        "amount", "charge", "revenue", "created_at", "business.name"
    ],
    "filters": {
        "date_from": "2023-11-01T00:00:00",
        "date_to": "2023-11-30T23:59:59"
    }
}

response = requests.post(
    "https://api.sagecloud.com/api/v1/exports/",
    json=data,
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)
```

### Export Audit Logs
```python
data = {
    "export_type": "audit_logs",
    "model_name": "audit.AuditLog",
    "fields_to_export": [
        "id", "email", "action", "description", 
        "ip_address", "status", "created_at"
    ],
    "filters": {
        "date_from": "2023-11-01T00:00:00",
        "date_to": "2023-11-30T23:59:59"
    }
}
```

## Email Templates

The system sends two types of emails:

1. **Export Ready**: When export is completed successfully
2. **Export Failed**: When export fails

Templates are located in:
- `templates/emails/export_ready_template.html`
- `templates/emails/export_ready_template.txt`
- `templates/emails/export_failed_template.html`
- `templates/emails/export_failed_template.txt`

## Security Considerations

- Users can only export data from their own business
- Business owners have additional access to audit logs
- Sensitive fields are marked and can be excluded
- Download links expire after 7 days
- All export activities are logged for audit purposes

## Performance

- Large datasets are processed in chunks (1000 records at a time)
- Background processing prevents API timeouts
- Progress tracking for long-running exports
- Automatic cleanup of expired files

## Monitoring

- Export requests are tracked in the database
- Celery tasks can be monitored through Celery monitoring tools
- Failed exports are logged with error details
- Email notifications keep users informed

## Troubleshooting

### Common Issues

1. **S3 Upload Fails**: Check AWS credentials and bucket permissions
2. **Email Not Sent**: Verify SMTP settings and email templates
3. **Export Stuck in Processing**: Check Celery worker status
4. **Permission Denied**: Ensure user has business association

### Logs

Check these log locations:
- Django logs: Application logs
- Celery logs: Task processing logs
- S3 logs: File upload/download logs

## Future Enhancements

- Support for additional file formats (Excel, JSON)
- Scheduled/recurring exports
- Export templates for common use cases
- Data transformation options
- Compression for large files
- Export sharing between business users
