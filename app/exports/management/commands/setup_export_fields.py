from django.core.management.base import BaseCommand
from exports.services import ExportFieldManager


class Command(BaseCommand):
    help = 'Setup exportable fields for supported models'
    
    def handle(self, *args, **options):
        self.stdout.write('Setting up exportable fields...')
        
        # Transaction fields
        transaction_fields = [
            {'field_name': 'id', 'field_label': 'ID', 'field_type': 'CharField', 'is_default': True, 'order': 1},
            {'field_name': 'reference', 'field_label': 'Reference', 'field_type': 'CharField', 'is_default': True, 'order': 2},
            {'field_name': 'merchant_reference', 'field_label': 'Merchant Reference', 'field_type': 'CharField', 'is_default': True, 'order': 3},
            {'field_name': 'status', 'field_label': 'Status', 'field_type': 'Char<PERSON>ield', 'is_default': True, 'order': 4},
            {'field_name': 'mode', 'field_label': 'Mode', 'field_type': 'Char<PERSON><PERSON>', 'is_default': True, 'order': 5},
            {'field_name': 'txn_class', 'field_label': 'Transaction Class', 'field_type': 'Char<PERSON><PERSON>', 'is_default': True, 'order': 6},
            {'field_name': 'type', 'field_label': 'Type', 'field_type': 'CharField', 'is_default': True, 'order': 7},
            {'field_name': 'amount', 'field_label': 'Amount', 'field_type': 'DecimalField', 'is_default': True, 'order': 8},
            {'field_name': 'charge', 'field_label': 'Charge', 'field_type': 'DecimalField', 'is_default': True, 'order': 9},
            {'field_name': 'revenue', 'field_label': 'Revenue', 'field_type': 'DecimalField', 'is_default': True, 'order': 10},
            {'field_name': 'net_amount', 'field_label': 'Net Amount', 'field_type': 'DecimalField', 'is_default': True, 'order': 11},
            {'field_name': 'old_balance', 'field_label': 'Old Balance', 'field_type': 'DecimalField', 'is_default': False, 'order': 12},
            {'field_name': 'new_balance', 'field_label': 'New Balance', 'field_type': 'DecimalField', 'is_default': False, 'order': 13},
            {'field_name': 'narration', 'field_label': 'Narration', 'field_type': 'TextField', 'is_default': True, 'order': 14},
            {'field_name': 'business.name', 'field_label': 'Business Name', 'field_type': 'CharField', 'is_default': True, 'order': 15},
            {'field_name': 'wallet.type', 'field_label': 'Wallet Type', 'field_type': 'CharField', 'is_default': False, 'order': 16},
            {'field_name': 'created_at', 'field_label': 'Created At', 'field_type': 'DateTimeField', 'is_default': True, 'order': 17},
            {'field_name': 'updated_at', 'field_label': 'Updated At', 'field_type': 'DateTimeField', 'is_default': False, 'order': 18},
        ]
        
        ExportFieldManager.setup_model_fields('transaction.Transaction', transaction_fields)
        self.stdout.write(self.style.SUCCESS('✓ Transaction fields setup complete'))
        
        # Audit Log fields
        audit_log_fields = [
            {'field_name': 'id', 'field_label': 'ID', 'field_type': 'CharField', 'is_default': True, 'order': 1},
            {'field_name': 'email', 'field_label': 'User Email', 'field_type': 'EmailField', 'is_default': True, 'order': 2},
            {'field_name': 'action', 'field_label': 'Action', 'field_type': 'CharField', 'is_default': True, 'order': 3},
            {'field_name': 'description', 'field_label': 'Description', 'field_type': 'TextField', 'is_default': True, 'order': 4},
            {'field_name': 'ip_address', 'field_label': 'IP Address', 'field_type': 'GenericIPAddressField', 'is_default': True, 'order': 5},
            {'field_name': 'user_agent', 'field_label': 'User Agent', 'field_type': 'TextField', 'is_default': False, 'order': 6},
            {'field_name': 'status', 'field_label': 'Status', 'field_type': 'CharField', 'is_default': True, 'order': 7},
            {'field_name': 'resource_type', 'field_label': 'Resource Type', 'field_type': 'CharField', 'is_default': True, 'order': 8},
            {'field_name': 'resource_id', 'field_label': 'Resource ID', 'field_type': 'CharField', 'is_default': False, 'order': 9},
            {'field_name': 'old_values', 'field_label': 'Old Values', 'field_type': 'JSONField', 'is_default': False, 'is_sensitive': True, 'order': 10},
            {'field_name': 'new_values', 'field_label': 'New Values', 'field_type': 'JSONField', 'is_default': False, 'is_sensitive': True, 'order': 11},
            {'field_name': 'metadata', 'field_label': 'Metadata', 'field_type': 'JSONField', 'is_default': False, 'order': 12},
            {'field_name': 'session_id', 'field_label': 'Session ID', 'field_type': 'CharField', 'is_default': False, 'order': 13},
            {'field_name': 'request_id', 'field_label': 'Request ID', 'field_type': 'CharField', 'is_default': False, 'order': 14},
            {'field_name': 'created_at', 'field_label': 'Created At', 'field_type': 'DateTimeField', 'is_default': True, 'order': 15},
        ]
        
        ExportFieldManager.setup_model_fields('audit.AuditLog', audit_log_fields)
        self.stdout.write(self.style.SUCCESS('✓ Audit Log fields setup complete'))
        
        # Wallet fields (for commission data)
        wallet_fields = [
            {'field_name': 'id', 'field_label': 'ID', 'field_type': 'CharField', 'is_default': True, 'order': 1},
            {'field_name': 'type', 'field_label': 'Wallet Type', 'field_type': 'CharField', 'is_default': True, 'order': 2},
            {'field_name': 'balance', 'field_label': 'Balance', 'field_type': 'DecimalField', 'is_default': True, 'order': 3},
            {'field_name': 'bank_name', 'field_label': 'Bank Name', 'field_type': 'CharField', 'is_default': True, 'order': 4},
            {'field_name': 'account_number', 'field_label': 'Account Number', 'field_type': 'CharField', 'is_default': True, 'order': 5},
            {'field_name': 'account_name', 'field_label': 'Account Name', 'field_type': 'CharField', 'is_default': True, 'order': 6},
            {'field_name': 'business.name', 'field_label': 'Business Name', 'field_type': 'CharField', 'is_default': True, 'order': 7},
            {'field_name': 'business.owner.email', 'field_label': 'Owner Email', 'field_type': 'EmailField', 'is_default': True, 'order': 8},
            {'field_name': 'created_at', 'field_label': 'Created At', 'field_type': 'DateTimeField', 'is_default': True, 'order': 9},
            {'field_name': 'updated_at', 'field_label': 'Updated At', 'field_type': 'DateTimeField', 'is_default': False, 'order': 10},
        ]
        
        ExportFieldManager.setup_model_fields('wallet.Wallet', wallet_fields)
        self.stdout.write(self.style.SUCCESS('✓ Wallet fields setup complete'))
        
        self.stdout.write(self.style.SUCCESS('All export fields setup completed successfully!'))
        self.stdout.write('You can now use the export API to export data from these models.')
        self.stdout.write('Available models: transaction.Transaction, audit.AuditLog, wallet.Wallet')
