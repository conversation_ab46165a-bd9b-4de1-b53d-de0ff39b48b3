from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from business.models import Business
from exports.models import ExportField
from exports.services import ExportService
from exports.serializers import CreateExportRequestSerializer

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the export system functionality'

    def handle(self, *args, **options):
        """Test the export system functionality"""
        self.stdout.write('🧪 Testing Export System...')
        
        # Test 1: Check if export fields are configured
        self.stdout.write('\n1. Checking export field configuration...')
        
        models_to_check = ['transaction.Transaction', 'audit.AuditLog', 'wallet.Wallet']
        for model_name in models_to_check:
            field_count = ExportField.objects.filter(model_name=model_name).count()
            default_count = ExportField.objects.filter(model_name=model_name, is_default=True).count()
            self.stdout.write(f'   ✓ {model_name}: {field_count} fields ({default_count} default)')
            
            if field_count == 0:
                self.stdout.write(
                    self.style.ERROR(f'   ❌ No fields configured for {model_name}. Run: python manage.py setup_export_fields')
                )
                return
        
        # Test 2: Test ExportService
        self.stdout.write('\n2. Testing ExportService...')
        
        try:
            service = ExportService()
            
            # Test getting available fields
            available_fields = service.get_available_fields('transaction.Transaction')
            self.stdout.write(f'   ✓ Available fields for transactions: {len(available_fields)}')
            
            # Test getting default fields
            default_fields = service.get_default_fields('transaction.Transaction')
            self.stdout.write(f'   ✓ Default fields for transactions: {len(default_fields)}')
            
            if not default_fields:
                self.stdout.write(self.style.ERROR('   ❌ No default fields configured'))
                return
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ ExportService error: {str(e)}'))
            return
        
        # Test 3: Test serializer validation
        self.stdout.write('\n3. Testing serializer validation...')
        
        try:
            # Test with valid data
            valid_data = {
                'export_type': 'transactions',
                'model_name': 'transaction.Transaction',
                'fields_to_export': default_fields[:3],  # Use first 3 default fields
                'filters': {
                    'date_from': '2023-01-01T00:00:00',
                    'date_to': '2023-12-31T23:59:59'
                }
            }
            
            serializer = CreateExportRequestSerializer(data=valid_data)
            if serializer.is_valid():
                self.stdout.write('   ✓ Valid data passes validation')
            else:
                self.stdout.write(self.style.ERROR(f'   ❌ Valid data failed validation: {serializer.errors}'))
                return
            
            # Test with empty fields (should auto-select defaults)
            empty_fields_data = {
                'export_type': 'transactions',
                'model_name': 'transaction.Transaction',
                'fields_to_export': [],
                'filters': {}
            }
            
            serializer = CreateExportRequestSerializer(data=empty_fields_data)
            if serializer.is_valid():
                validated_data = serializer.validated_data
                if validated_data['fields_to_export']:
                    self.stdout.write('   ✓ Empty fields auto-populated with defaults')
                    self.stdout.write(f'     Auto-selected fields: {validated_data["fields_to_export"]}')
                else:
                    self.stdout.write(self.style.ERROR('   ❌ Empty fields not auto-populated'))
                    return
            else:
                self.stdout.write(self.style.ERROR(f'   ❌ Empty fields validation failed: {serializer.errors}'))
                return
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Serializer test error: {str(e)}'))
            return
        
        # Test 4: Test model creation
        self.stdout.write('\n4. Testing model creation...')
        
        try:
            # Create test user and business if they don't exist
            user, user_created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'firstname': 'Test',
                    'lastname': 'User',
                    'role': 'Business_Owner'
                }
            )

            if user_created:
                self.stdout.write('   ✓ Created test user')
            else:
                self.stdout.write('   ✓ Using existing test user')

            business, business_created = Business.objects.get_or_create(
                owner=user,
                defaults={'name': 'Test Business'}
            )

            if business_created:
                self.stdout.write('   ✓ Created test business')
            else:
                self.stdout.write('   ✓ Using existing test business')
            
            # Test creating export request using the service (proper way)
            service = ExportService()
            export_request = service.create_export_request(
                user=user,
                business=business,
                model_name='transaction.Transaction',
                export_type='transactions',
                fields_to_export=['id', 'reference', 'amount']
            )
            
            self.stdout.write(f'   ✓ Export request created: {export_request.id}')
            self.stdout.write(f'   ✓ Status: {export_request.status}')
            self.stdout.write(f'   ✓ Progress: {export_request.progress_percentage}%')
            
            # Clean up
            export_request.delete()
            
        except Exception as e:
            error_msg = str(e)
            if 'Invalid role' in error_msg:
                self.stdout.write(self.style.ERROR(f'   ❌ Role validation error: {error_msg}'))
                self.stdout.write('   💡 Available roles: Admin, Initiator, Verifier, Approver, Business_Owner')
            else:
                self.stdout.write(self.style.ERROR(f'   ❌ Model creation error: {error_msg}'))
            return
        
        # Test 5: Test field value extraction
        self.stdout.write('\n5. Testing field value extraction...')
        
        try:
            service = ExportService()
            
            # Test with the business object
            test_fields = ['id', 'name', 'owner.email']
            for field_name in test_fields:
                try:
                    value = service._get_field_value(business, field_name)
                    self.stdout.write(f'   ✓ {field_name}: {value}')
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'   ❌ Error extracting {field_name}: {str(e)}'))
                    return
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Field extraction test error: {str(e)}'))
            return
        
        self.stdout.write(self.style.SUCCESS('\n🎉 All tests passed! Export system is working correctly.'))
        self.stdout.write('\nNext steps:')
        self.stdout.write('1. Test API endpoints with your frontend or API client')
        self.stdout.write('2. Try creating an export with empty fields to see auto-selection')
        self.stdout.write('3. Monitor Celery workers for background processing')
        
        # Show example API call
        self.stdout.write('\nExample API call (with empty fields):')
        self.stdout.write('curl -X POST "http://localhost:8000/api/v1/exports/" \\')
        self.stdout.write('  -H "Authorization: Bearer YOUR_TOKEN" \\')
        self.stdout.write('  -H "Content-Type: application/json" \\')
        self.stdout.write('  -d \'{"export_type": "transactions", "model_name": "transaction.Transaction", "fields_to_export": []}\'')
