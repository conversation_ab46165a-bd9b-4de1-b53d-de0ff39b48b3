from rest_framework import serializers
from django.contrib.contenttypes.models import ContentType
from django.apps import apps
from .models import ExportRequest, ExportField
from .services import ExportService


class ExportFieldSerializer(serializers.ModelSerializer):
    """Serializer for ExportField model"""
    
    class Meta:
        model = ExportField
        fields = [
            'field_name', 'field_label', 'field_type', 
            'is_default', 'is_sensitive', 'order'
        ]


class ExportRequestSerializer(serializers.ModelSerializer):
    """Serializer for ExportRequest model"""
    
    #progress_percentage = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    export_type_display = serializers.CharField(source='get_export_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = ExportRequest
        fields = [
            'id', 'export_type', 'export_type_display', 'model_name',
            'filename', 'status', 'status_display', 'total_records',
            'processed_records', 'file_size',
            'download_url', 'error_message', 'expires_at', 'is_expired',
            'email_sent', 'email_sent_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'filename', 'status', 'total_records', 'processed_records',
            'file_size', 'download_url', 'error_message', 'expires_at',
            'email_sent', 'email_sent_at', 'created_at', 'updated_at'
        ]


class CreateExportRequestSerializer(serializers.Serializer):
    """Serializer for creating export requests"""
    
    SUPPORTED_MODELS = {
        'transactions': 'transaction.Transaction',
        'audit_logs': 'audit.AuditLog',
        'wallets': 'wallet.Wallet',  # Commission wallet transactions
        #add other models to be exported here

    }
    
    export_type = serializers.ChoiceField(
        choices=ExportRequest.ExportType.choices,
        help_text="Type of data to export"
    )
    
    model_name = serializers.CharField(
        help_text="Model to export data from (e.g., 'transaction.Transaction')"
    )
    
    fields_to_export = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True,
        help_text="List of field names to include in the export. If empty, default fields will be used."
    )
    
    filters = serializers.JSONField(
        required=False,
        default=dict,
        help_text="Filters to apply to the data (e.g., date ranges, status filters)"
    )
    
    def validate_model_name(self, value):
        """Validate that the model exists and is supported"""
        try:
            app_label, model_name = value.split('.')
            model_class = apps.get_model(app_label, model_name)
        except (ValueError, LookupError):
            raise serializers.ValidationError(f"Invalid model name: {value}")
        
        # Check if model is in supported models
        if value not in self.SUPPORTED_MODELS.values():
            supported = list(self.SUPPORTED_MODELS.keys())
            raise serializers.ValidationError(
                f"Model {value} is not supported for export. "
                f"Supported types: {', '.join(supported)}"
            )
        
        return value
    
    def validate_fields_to_export(self, value):
        """Validate that the fields exist for the model"""
        # Allow empty list - we'll use default fields in that case
        return value
    
    def validate_filters(self, value):
        """Validate filter format"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Filters must be a dictionary")
        
        # Validate date filters if present
        if 'date_from' in value and value['date_from']:
            try:
                from datetime import datetime
                datetime.fromisoformat(value['date_from'].replace('Z', '+00:00'))
            except ValueError:
                raise serializers.ValidationError("Invalid date_from format. Use ISO format.")
        
        if 'date_to' in value and value['date_to']:
            try:
                from datetime import datetime
                datetime.fromisoformat(value['date_to'].replace('Z', '+00:00'))
            except ValueError:
                raise serializers.ValidationError("Invalid date_to format. Use ISO format.")
        
        return value
    
    def validate(self, attrs):
        """Cross-field validation"""
        model_name = attrs.get('model_name')
        fields_to_export = attrs.get('fields_to_export', [])

        if model_name:
            export_service = ExportService()
            available_fields = export_service.get_available_fields(model_name)
            available_field_names = [f['field_name'] for f in available_fields]

            # If no fields specified, use default fields
            if not fields_to_export:
                default_fields = export_service.get_default_fields(model_name)
                if not default_fields:
                    # If no default fields configured, use first 5 available fields
                    attrs['fields_to_export'] = available_field_names[:5] if available_field_names else []
                else:
                    attrs['fields_to_export'] = default_fields
            else:
                # Validate specified fields exist
                invalid_fields = [f for f in fields_to_export if f not in available_field_names]
                if invalid_fields:
                    raise serializers.ValidationError({
                        'fields_to_export': f"Invalid fields for model {model_name}: {', '.join(invalid_fields)}"
                    })

            # # Ensure we have at least one field to export
            # if not attrs.get('fields_to_export'):
            #     raise serializers.ValidationError({
            #         'fields_to_export': f"No exportable fields configured for model {model_name}. Please run setup_export_fields command."
            #     })

        return attrs



# class ExportStatusSerializer(serializers.Serializer):
#     """Serializer for export status response"""
    
#     id = serializers.IntegerField()
#     status = serializers.CharField()
#     progress_percentage = serializers.FloatField()
#     total_records = serializers.IntegerField(allow_null=True)
#     processed_records = serializers.IntegerField()
#     error_message = serializers.CharField(allow_null=True)
#     download_url = serializers.URLField(allow_null=True)
#     expires_at = serializers.DateTimeField(allow_null=True)
